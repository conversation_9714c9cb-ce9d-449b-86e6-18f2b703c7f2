#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "Threading/ThreadedWorkManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include <thread>
#include <sstream>

// 스레드 로컬 시퀀스 스토리지 정의
thread_local NSDataBaseManager::ThreadLocalSequence NSDataBaseManager::t_localSequence;

NSDataBaseManager::NSDataBaseManager()
{
}

NSDataBaseManager::~NSDataBaseManager()
{
    Finalize();
}

bool NSDataBaseManager::Initialize()
{
    if (m_initialized.exchange(true))
        return true;

    try
    {
        // 스레드별 작업 매니저 생성
        m_workerManager = std::make_unique<ThreadedWorkManager>();
        
        // 연결 풀 생성 (아직 초기화하지 않음)
        for (int i = 0; i < 12; ++i)
        {
            m_connectionPools[i] = std::make_unique<NSMySQLConnectionPool>();
        }

        return true;
    }
    catch (const std::exception& e)
    {
        m_initialized = false;
        return false;
    }
}

void NSDataBaseManager::Finalize()
{
    if (!m_initialized.exchange(false))
        return;

    Stop();

    // 연결 풀 종료
    for (auto& pool : m_connectionPools)
    {
        if (pool)
        {
            pool->Finalize();
            pool.reset();
        }
    }
}

bool NSDataBaseManager::AddConnectionInfo(int32 dbType, int32 shardId, const std::string& host, int port,
                                         const std::string& dbName, const std::string& user, const std::string& password)
{
    auto* pool = GetConnectionPool(dbType, shardId);
    if (!pool)
        return false;
        
    return pool->AddConnectionInfo(host, port, dbName, user, password);
}

NSMySQLConnectionPool* NSDataBaseManager::GetDBConnection(int32 dbType, int32 shardId)
{
    return GetConnectionPool(dbType, shardId);
}

void NSDataBaseManager::ReconnectConnection(int32 dbType, int32 shardId)
{
    auto* pool = GetConnectionPool(dbType, shardId);
    if (pool)
    {
        pool->Reconnect();
    }
}

int64_t NSDataBaseManager::GetDBQueueSize() const
{
    if (!m_workerManager)
        return 0;
    return m_workerManager->GetQueueSize();
}

std::string NSDataBaseManager::GetConnectionPoolCountInfo() const
{
    std::stringstream ss;
    for (int i = 0; i < 12; ++i)
    {
        if (m_connectionPools[i])
        {
            ss << "Pool[" << i << "]: " << m_connectionPools[i]->GetActiveConnectionCount() 
               << "/" << m_connectionPools[i]->GetTotalConnectionCount() << " ";
        }
    }
    return ss.str();
}

std::string NSDataBaseManager::GetConnectionPoolCountLog() const
{
    return GetConnectionPoolCountInfo(); // 동일한 정보 반환
}

NSMySQLConnectionPool* NSDataBaseManager::GetConnectionPool(int32 dbType, int32 shardId)
{
    int poolIndex = -1;

    switch (dbType)
    {
    case 0: // GameDB
        if (shardId >= 0 && shardId < 10)
            poolIndex = shardId;
        break;
    case 1: // CommonDB
        poolIndex = 10;
        break;
    case 2: // LogDB
        poolIndex = 11;
        break;
    }

    if (poolIndex >= 0 && poolIndex < 12)
        return m_connectionPools[poolIndex].get();

    return nullptr;
}

// 시퀀스 관리 메서드 구현 (단순화)
int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    // 스레드 로컬 스토리지 사용 - 락 불필요
    return ++t_localSequence.sequenceByCid[cid];
}

void NSDataBaseManager::OnSessionClosed(int64_t cid)
{
    // 스레드 로컬 스토리지에서 제거 - 락 불필요
    t_localSequence.sequenceByCid.erase(cid);
}

template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    const NS::Connection& connection,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // 연결 풀 가져오기
        auto* pool = GetConnectionPool(connection.DatabaseType, connection.ShardId);
        if (!pool)
        {
            m_pushAccessCount--;
            promise.SetException(std::make_exception_ptr(
                std::runtime_error("Invalid connection pool")));
            return;
        }

        // 샤드키 기반 스레드 선택
        uint64_t shardKey = 0;
        if constexpr (requires { SP::Input; })
        {
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            if constexpr (requires { input.Cid; })
                shardKey = input.Cid;
            else if constexpr (requires { input.Aid; })
                shardKey = input.Aid;
        }
        
        int threadIndex = GetExecutorByShardKey(shardKey);
        
        m_queriesProcessing++;
        m_inputCount++;
        
        // 스레드별 워커에서 비동기 실행
        m_workerManager->PostWork([=]() mutable
        {
            // RAII 가드로 atomic counter 보장
            struct CounterGuard {
                std::atomic<int>& queries;
                std::atomic<int>& access;
                ~CounterGuard() {
                    queries--;
                    access--;
                }
            } guard{m_queriesProcessing, m_pushAccessCount};

            auto queryData = std::make_shared<NSQueryData>();
            std::unique_ptr<NSMySQLConnection> conn;
            
            try
            {
                // 연결 획득 - 스레드 인덱스 전달
                conn = pool->GetConnection(threadIndex);
                if (!conn)
                {
                    throw std::runtime_error("Failed to get connection");
                }

                // 저장 프로시저 실행
                SP sp;
                auto result = sp.Execute(conn.get(), serializer);
                queryData->SetErrorCode(result);
                
                m_outputCount++;
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);

                // 결과 반환
                promise.SetValue(queryData);

                // 연결 반환 - 스레드 인덱스 전달
                pool->ReturnConnection(std::move(conn), threadIndex);
            }
            catch (const std::exception& e)
            {
                // 연결 반환 보장
                if (conn)
                {
                    pool->ReturnConnection(std::move(conn), threadIndex);
                }
                
                // 에러 로깅
                LOGE << "Query execution failed: " << e.what() 
                     << " [SP: " << SP::GetName() << ", ThreadIndex: " << threadIndex << "]";
                
                promise.SetException(std::current_exception());
            }
            catch (...)
            {
                // 연결 반환 보장
                if (conn)
                {
                    pool->ReturnConnection(std::move(conn), threadIndex);
                }
                
                LOGE << "Unknown exception in query execution"
                     << " [SP: " << SP::GetName() << ", ThreadIndex: " << threadIndex << "]";
                
                promise.SetException(std::current_exception());
            }
        }, threadIndex);
    });
}

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
        
    // 스레드 수 결정
    if (workThreadCnt == 0)
        workThreadCnt = std::thread::hardware_concurrency() * 2 + 16;
    
    m_threadCount = workThreadCnt;
    
    // IOCP 워커 매니저 시작
    if (!m_workerManager->Initialize(workThreadCnt))
        return false;
        
    // 연결 풀 초기화
    // GameDB 0-9 (10개)
    for (int i = 0; i < 10; ++i)
    {
        if (!m_connectionPools[i]->Initialize(0, i)) // DatabaseType=0, ShardId=i
            return false;
    }

    // CommonDB (인덱스 10)
    if (!m_connectionPools[10]->Initialize(1, 0)) // DatabaseType=1, ShardId=0
        return false;

    // LogDB (인덱스 11)
    if (!m_connectionPools[11]->Initialize(2, 0)) // DatabaseType=2, ShardId=0
        return false;
        
    return true;
}

void NSDataBaseManager::Stop()
{
    ProhibitPushAccess();
    StopAllWorkerThreadAndWait();
}

void NSDataBaseManager::ProhibitPushAccess()
{
    m_pushAccessProhibit = true;
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
    // 모든 큐 작업이 완료될 때까지 대기
    while (m_pushAccessCount > 0 || m_queriesProcessing > 0)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 워커 매니저 종료
    if (m_workerManager)
    {
        m_workerManager->Finalize();
    }
}

DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StorageUpdateQuery(
    std::shared_ptr<NSStorageUpdateContainer> containerData,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
    std::shared_ptr<NSDBSession> session,
    std::source_location location)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
        
        // CID 기반 스레드 선택
        int threadIndex = GetExecutorByShardKey(containerData->Cid);
        
        m_queriesProcessing++;
        
        // 작업 큐에 추가 - CID 기반 스레드 선택
        m_workerManager->PostWork([=]() mutable
        {
            try {
                // Query 실행
                EErrorCode queryResult = EErrorCode::None;
                if (pQueryFunc)
                {
                    queryResult = pQueryFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // Result 처리
                if (pResultFunc)
                {
                    queryResult = pResultFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);
                
                promise.SetValue(queryData);
            }
            catch (...) {
                promise.SetException(std::current_exception());
            }
            
            m_queriesProcessing--;
            m_pushAccessCount--;
        }, threadIndex);
    });
}